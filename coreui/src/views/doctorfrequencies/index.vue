<template>
  <c-row>
    <c-col col="12" xl="12">
      <transition name="slide">
        <c-card>
          <c-card-header> Doctor Frequencies </c-card-header>
          <c-card-body>
            <c-button color="primary" v-if="checkPermission('create_doctor_frequencies')"
              :to="{ name: 'CreateDoctorFrequency' }">Create Doctor Frequency</c-button>
            <CDropdown placement="bottom-end" toggler-text="Tools" color="primary" class="float-right d-inline-block">
              <CDropdownItem v-if="checkPermission('download_all_templates')"
                @click="templateDownload('DoctorFrequency_Template.xlsx')">Template</CDropdownItem>

              <c-dropdown-item v-if="checkPermission('import_doctor_frequencies')" @click="successModal = true">Upload
                New</c-dropdown-item>

              <c-dropdown-item v-if="checkPermission('import_bulk_edit')" @click="updateModal = true">Bulk
                Edit</c-dropdown-item>

              <c-dropdown-divider></c-dropdown-divider>

              <CDropdownItem v-if="checkPermission('export_xlsx_doctor_frequencies')" @click="exportDoctorFrequency()">
                Export to Excel</CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_csv_doctor_frequencies')" @click="exportCSV()">Export to CSV
              </CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_pdf_doctor_frequencies')"
                @click="exportDoctorFrequencyPDF()">Export to PDF</CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_email_doctor_frequencies')" @click="sendModal = true">Send to
                Mail</CDropdownItem>
            </CDropdown>

            <CModal title="Upload Doctor Frequency" color="success" :show.sync="successModal">
              <CInputFile type="file" ref="file" id="file" name="file_name" v-on:change="handleFileUpload"
                placeholder="New file" />
              <CProgress :value="uploadPercentage" color="success" animated showPercentage show-value
                style="height: 15px" class="mt-1" :max="100" v-show="progressBar" />
              <template #footer>
                <CButton class="text-white" @click="successModal = false" color="danger">Discard</CButton>
                <CButton class="text-white" @click="importDoctorFrequencies()" color="success">Upload</CButton>
              </template>
            </CModal>

            <!-- bulk edit - update modal -->
            <c-modal title="Bulk Edit Doctor Frequencies" color="success" :show.sync="updateModal">
              <c-input-file type="file" ref="file" id="bulkEditFile" name="file_name" v-on:change="handleFileUpload"
                placeholder="New file" />
              <c-progress :value="uploadPercentage" color="success" animated showPercentage show-value
                style="height: 15px" class="mt-1" :max="100" v-show="progressBar" />
              <template #footer>
                <c-button @click="updateModal = false" color="danger">Discard</c-button>
                <c-button @click="importUpdateDoctorFrequency()" class="text-white" color="success">Upload</c-button>
              </template>
            </c-modal>

            <CModal title="Compose" color="success" :show.sync="sendModal">
              <template>
                <div class="form-group">
                  <vue-tags-input v-model="email" :tags="emails" name="email[]" :validation="validation"
                    placeholder="To" :add-on-key="addOnKey" @tags-changed="(newEmails) => (emails = newEmails)" />
                </div>
              </template>

              <CTextarea type="text" name="text" v-model="text" placeholder="Message"></CTextarea>
              <template #footer>
                <CButton class="text-white" @click="sendModal = false" color="danger">Discard</CButton>
                <CButton class="text-white" @click="sendDoctorFrequencyMail()" color="success">Send</CButton>
              </template>
            </CModal>
            <br />
            <div>
              <c-button style="float: right;background-color: blueviolet;" class="text-white" @click="getData()">
                <CIcon name="cil-reload" />
              </c-button>
            </div>
            <br /><br />
            <c-data-table hover striped sorter footer itemsPerPageSelect :items="items" :fields="fields"
              :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top>
              <template slot="cleaner">
                <label class="mfe-2">Filter: </label>
                <c-input v-model="search" placeholder="type string..." type="text" />
              </template>
              <template #date="{ item }">
                <td>{{ format_date(item.date) }}</td>
              </template>

              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ totalData }}
                </td>
              </template>

              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <c-button color="success" class="btn-sm mt-2 mr-1" :to="{
                      name: 'EditDoctorFrequency',
                      params: { id: item.id },
                    }" v-if="checkPermission('edit_doctor_frequencies')"><i class="cil-pencil"></i>
                      <CIcon name="cil-pencil" />
                    </c-button>
                    <c-button color="danger" v-if="checkPermission('delete_doctor_frequencies')"
                      class="btn-sm mt-2 mr-1" @click="
                        $root
                          .$confirm(
                            'Delete',
                            'Do you want to delete this record?',
                            {
                              color: 'red',
                              width: 290,
                              zIndex: 200,
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              deleteDoctorFrequency(item);
                            }
                          })
                        "><c-icon name="cil-trash" /></c-button>
                  </div>
                </td>
              </template>
            </c-data-table>
            <c-pagination v-if="items.length != 0" :activePage.sync="page" @update:activePage="getData()"
              :pages="total" />
          </c-card-body>
          <c-card-footer> <c-button color="primary" style="float: right"
              @click="replicate">Replicate</c-button></c-card-footer>
        </c-card>
      </transition>
    </c-col>
  </c-row>
</template>

<script>
import moment from "moment";
import VueTagsInput from "@johmun/vue-tags-input";
export default {
  components: {
    VueTagsInput,
  },
  data() {
    return {
      uploadPercentage: 0,
      progressBar: false,
      addOnKey: [13, 32, ":", ";"],
      items: [],
      fields: [
        "id",
        "line",
        "account",
        "doctor",
        "frequency",
        "date",
        "actions",
      ],
      successModal: false,
      updateModal: false,
      sendModal: false,
      file: "",
      file_name: "",
      text: "",
      validation: [
        {
          classes: "email",
          rule: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/,
        },
      ],
      email: "",
      emails: [],
      search: null,
      page: 1,
      total: 0,
      totalData: 0,
      previousTimeout: null,
    };
  },
  methods: {
    removeDoctorFrequency(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteDoctorFrequency(item) {
      axios
        .delete(`/api/doctorfrequencies/${item.id}`)
        .then((res) => {
          this.removeDoctorFrequency(item);
          this.flash("Doctor Frequency Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    importDoctorFrequencies() {
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/import-doctor-frequency", formData);
      document.getElementById("file").value = "";
      this.successModal = false;
    },

    importUpdateDoctorFrequency() {
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/import-update-doctor-frequency", formData);
      document.getElementById("bulkEditFile").value = "";
      this.updateModal = false;
    },

    handleFileUpload(files, event) {
      this.file = event.target.files[0];
      this.file_name = event.target.files[0].name;
    },
    exportDoctorFrequency() {
      this.exportFile("/api/export-doctor-frequency", "DoctorFrequency.xlsx");
    },
    exportCSV() {
      this.exportFile(
        "/api/export-doctor-frequency-csv",
        "DoctorFrequency.csv"
      );
    },
    exportDoctorFrequencyPDF() {
      this.exportFile(
        "/api/export-doctor-frequency-pdf",
        "DoctorFrequency.pdf"
      );
    },
    sendDoctorFrequencyMail() {
      const formData = {
        emails: JSON.stringify(this.emails),
        text: this.text,
      };
      this.sendMail("/api/send-mail-doctor-frequency", formData);
      this.successModal = false;
    },
    format_date(value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    timeoutClear() {
      if (this.previousTimeout) {
        clearTimeout(this.previousTimeout);
        this.previousTimeout = null;
      }
    },
    replicate() {
      axios
        .get(`/api/frequency-replicate`)
        .then((res) => {
          this.flash("Frequency replicated Successfully");
          // this.getData();
        })
        .catch((err) => this.showErrorMessage(err));
      this.getData();
    },
    getData() {
      // this.get("/api/plans", "plan_visits");
      axios
        .post("/api/doctorfrequencies/index?page=" + this.page, {
          query: this.search,
        })
        .then((res) => {
          this.items = res.data.data.doctor_frequencies.data;
          this.total = res.data.data.doctor_frequencies.last_page;
          this.totalData = res.data.data.doctor_frequencies.to;
        })
        .catch((err) => this.showErrorMessage(err));
    },
    // getData() {
    //   this.get("/api/doctorfrequencies", "doctor_frequencies");
    // },
  },
  // created() {
  //   this.getData();
  // },
  watch: {
    search() {
      this.timeoutClear();
      const id = setTimeout(() => this.getData(), 500);
      this.previousTimeout = id;
    },
  },
};
</script>
