<template>
  <div class="map-container">
    <!-- Map Controls for toggling user list -->
    <MapControls @toggle-user-list="isUserListVisible = !isUserListVisible" />

    <!-- Status Bar with connection info and stats -->
    <StatusBar :is-connected="isConnected" :connection-status="connectionStatus"
      :tracked-users-count="trackedUsersCount" :total-location-points="totalLocationPoints"
      :selected-user-id="selectedUserId"
      :selected-user-name="trackedUsers[selectedUserId]?.userName || selectedUserId" />

    <!-- Map Component -->
    <MapComponent ref="mapComponent" :tracked-users="trackedUsers" :show-all-tracks="showAllTracks"
      @map-initialized="onMapInitialized" />

    <!-- User List Panel -->
    <UserListPanel :visible="isUserListVisible" :users="filteredUsers" :users-count="trackedUsersCount"
      :selected-user-id="selectedUserId" :show-all-tracks="showAllTracks" :search-term="userSearchTerm"
      :sort-method="userSortMethod" @close="isUserListVisible = false" @select-user="centerMapOnUser"
      @toggle-user-track="toggleUserTrack" @toggle-all-tracks="toggleAllTracks" @show-user-history="showUserHistory"
      @update:search-term="userSearchTerm = $event" @update:sort-method="userSortMethod = $event"
      @fit-all="centerMapToFitAllUsers" />

    <!-- User History Modal -->
    <HistoryModal :show="showHistoryModal" :selected-user="selectedHistoryUser"
      :selected-user-id="selectedHistoryUserId" @close="closeHistoryModal" @export-data="exportUserData" />

    <div class="location-sending-info">
      <strong>Note on Sending Location:</strong> This component displays tracked locations. A separate part of your
      application would use the device's GPS to get coordinates (latitude, longitude)
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

// Import child components
import MapComponent from './components/MapComponent.vue';
import StatusBar from './components/StatusBar.vue';
import UserListPanel from './components/UserListPanel.vue';
import HistoryModal from './components/HistoryModal.vue';
import MapControls from './components/MapControls.vue';

const getUserColor = (userId) => {
  const colors = ['#2ecc40'];
  return colors[parseInt(userId, 10) % colors.length];
};

export default {
  components: {
    MapComponent,
    StatusBar,
    UserListPanel,
    HistoryModal,
    MapControls
  },
  data() {
    return {
      map: null,
      trackedUsers: {},
      isConnected: false,
      connectionStatus: 'Connecting...',
      selectedUserId: null,
      userSearchTerm: '',
      userSortMethod: 'recent',
      showAllTracks: true,
      showHistoryModal: false,
      selectedHistoryUserId: null,
      totalLocationPoints: 0,
      debugInterval: null,
      isUserListVisible: false,
    }
  },
  computed: {
    ...mapState('app', ['onlineChannel']),
    trackedUsersCount() {
      return Object.keys(this.trackedUsers).length;
    },
    filteredUsers() {
      let users = { ...this.trackedUsers };

      // Apply search filter
      if (this.userSearchTerm) {
        const searchLower = this.userSearchTerm.toLowerCase();
        users = Object.fromEntries(
          Object.entries(users).filter(([userId, user]) => {
            const userName = (user.userName || `User ${userId}`).toLowerCase();
            return userName.includes(searchLower);
          })
        );
      }

      // Apply sorting
      const sortedEntries = Object.entries(users).sort((a, b) => {
        const [idA, userA] = a;
        const [idB, userB] = b;

        if (this.userSortMethod === 'name') {
          const nameA = (userA.userName || `User ${idA}`).toLowerCase();
          const nameB = (userB.userName || `User ${idB}`).toLowerCase();
          return nameA.localeCompare(nameB);
        } else if (this.userSortMethod === 'recent') {
          return (userB.lastUpdate || 0) - (userA.lastUpdate || 0);
        } else if (this.userSortMethod === 'oldest') {
          return (userA.lastUpdate || 0) - (userB.lastUpdate || 0);
        }

        return 0;
      });

      return Object.fromEntries(sortedEntries);
    },
    selectedHistoryUser() {
      return this.selectedHistoryUserId ? this.trackedUsers[this.selectedHistoryUserId] : null;
    }
  },
  methods: {
    onMapInitialized(map) {
      this.map = map;
      console.log('Map initialized and stored in parent component');
    },
    updateUserLocation(userId, lat, lng, options = {}) {
      if (!this.map) return; // Ensure map is initialized

      // Default options
      const {
        userName = `User ${userId}`,
        altitude = null,
        speed = null,
        heading = null,
        timestamp = Date.now()
      } = options;

      const userExists = userId in this.trackedUsers;
      const newCoords = L.latLng(lat, lng);

      // Update total location points
      this.totalLocationPoints++;

      if (!userExists) {
        // --- New User ---
        const color = getUserColor(userId);

        // Create custom marker with color
        const markerHtml = `
          <div class="custom-marker" style="background-color: ${color}; border: 2px solid white; border-radius: 50%; width: 14px; height: 14px;"></div>
        `;
        const customIcon = L.divIcon({
          html: markerHtml,
          className: 'custom-marker-container',
          iconSize: [20, 20],
          iconAnchor: [10, 10]
        });

        // Create Marker
        const newMarker = L.marker(newCoords, { icon: customIcon })
          .addTo(this.map)
          .bindPopup(this.createPopupContent(userId, lat, lng, userName, altitude, speed, heading));

        // Create Polyline for the track
        const newPolyline = L.polyline([], { color: color, weight: 3, opacity: 0.7 }).addTo(this.map);

        // Store user data with timestamps - using Vue.set for reactivity in Vue 2
        this.$set(this.trackedUsers, userId, {
          lat: lat,
          lng: lng,
          userName: userName,
          marker: newMarker,
          path: [], // Initialize path array with timestamps
          polyline: newPolyline,
          color: color,
          showTrack: this.showAllTracks, // Match global setting
          speed: speed,
          altitude: altitude,
          heading: heading,
          lastUpdate: timestamp
        });

        console.log(`Added user ${userId} (${userName})`);

        // Add first point to path with timestamp
        this.trackedUsers[userId].path.push({
          lat: lat,
          lng: lng,
          timestamp: timestamp,
          altitude: altitude,
          speed: speed,
          heading: heading
        });
        this.trackedUsers[userId].polyline.addLatLng(newCoords);

      } else {
        // --- Existing User ---
        const user = this.trackedUsers[userId];

        // Calculate speed if not provided but we have previous points
        let calculatedSpeed = speed;
        if (calculatedSpeed === null && user.path.length > 0) {
          const lastPoint = user.path[user.path.length - 1];
          const timeDiff = (timestamp - lastPoint.timestamp) / 1000; // seconds
          if (timeDiff > 0) {
            const distance = this.calculateDistance(
              lastPoint.lat, lastPoint.lng,
              lat, lng
            );
            calculatedSpeed = (distance / timeDiff) * 3600; // km/h
          }
        }

        // Update marker position
        user.marker.setLatLng(newCoords);
        user.lat = lat;
        user.lng = lng;
        user.speed = calculatedSpeed;
        user.altitude = altitude;
        user.heading = heading;
        user.lastUpdate = timestamp;

        // Update user name if it changed
        if (userName !== user.userName) {
          user.userName = userName;
        }

        // Update popup content
        user.marker.setPopupContent(this.createPopupContent(userId, lat, lng, userName, altitude, calculatedSpeed, heading));

        // Add new point to path with metadata
        user.path.push({
          lat: lat,
          lng: lng,
          timestamp: timestamp,
          altitude: altitude,
          speed: calculatedSpeed,
          heading: heading
        });

        if (user.showTrack && user.polyline) {
          user.polyline.addLatLng(newCoords);
        }

        // Optional: Limit path length to avoid performance issues
        const MAX_PATH_POINTS = 1000;
        if (user.path.length > MAX_PATH_POINTS) {
          user.path.shift(); // Remove the oldest point
          // Re-drawing the polyline might be needed
          if (user.path.length > 1) {
            const simplifiedPath = user.path.map(point => L.latLng(point.lat, point.lng));
            user.polyline.setLatLngs(simplifiedPath);
          }
        }
      }
    },
    centerMapOnUser(userId) {
      const user = this.trackedUsers[userId];
      if (this.map && user) {
        this.map.setView([user.lat, user.lng], 15); // Center and zoom
        user.marker.openPopup(); // Open the popup
        this.selectedUserId = userId; // Highlight in list
      }
    },
    connectWebSocket() {
      console.log(`Attempting to connect to WebSocket server at ${import.meta.env.VITE_WEBSOCKET_HOST}`);

      // Check if onlineChannel exists from Vuex store
      if (!this.onlineChannel) {
        console.error('onlineChannel is not available from the Vuex store');
        this.connectionStatus = 'Connection Error: Channel not available';
        return;
      }

      // Set up connection and status
      this.isConnected = this.onlineChannel.connected || false;
      this.connectionStatus = this.isConnected ? 'Connected' : 'Connecting...';

      if (this.isConnected) {
        console.log(`Connected to WebSocket server with ID: ${this.onlineChannel.id}`);
      }

      // Set up event listeners for WebSocket events
      this.onlineChannel.on('connect', () => {
        this.isConnected = true;
        this.connectionStatus = 'Connected';
        console.log(`Connected to WebSocket server with ID: ${this.onlineChannel.id}`);
      });

      this.onlineChannel.on('disconnect', (reason) => {
        this.isConnected = false;
        this.connectionStatus = `Disconnected (${reason})`;
        console.warn('Disconnected from WebSocket server:', reason);
      });

      this.onlineChannel.on('connect_error', (error) => {
        this.isConnected = false;
        this.connectionStatus = 'Connection Error';
        console.error('WebSocket Connection Error:', error);
      });

      // --- Main Event: User Location Update ---
      this.onlineChannel.on('user-location-update', (data) => {
        console.log('Received location update:', data);
        const { userId, lat, lng, userName, altitude, speed, heading, timestamp } = data;

        if (userId === undefined || lat === undefined || lng === undefined) {
          console.warn('Received incomplete location data:', data);
          return;
        }

        // Update user marker and track with all available data
        this.updateUserLocation(userId, lat, lng, {
          userName,
          altitude,
          speed,
          heading,
          timestamp: timestamp || Date.now()
        });
      });
    },
    toggleUserTrack(userId) {
      const user = this.trackedUsers[userId];
      if (!user || !this.map) return;

      user.showTrack = !user.showTrack;

      if (user.showTrack) {
        // If track was hidden, add polyline back to map
        if (user.polyline) {
          user.polyline.addTo(this.map);
          // Create points from stored path
          const points = user.path.map(point => L.latLng(point.lat, point.lng));
          user.polyline.setLatLngs(points);
        } else {
          // Should not happen if initialized correctly, but handle defensively
          const points = user.path.map(point => L.latLng(point.lat, point.lng));
          user.polyline = L.polyline(points, { color: user.color, weight: 3, opacity: 0.7 }).addTo(this.map);
        }
      } else {
        // If track should be hidden, remove polyline from map
        if (user.polyline) {
          this.map.removeLayer(user.polyline);
        }
      }
    },
    toggleAllTracks() {
      // Apply to all users
      Object.keys(this.trackedUsers).forEach(userId => {
        const user = this.trackedUsers[userId];

        // Only change if different from current global setting
        if (user.showTrack !== this.showAllTracks) {
          user.showTrack = this.showAllTracks;

          if (user.showTrack) {
            // Show track
            if (user.polyline) {
              user.polyline.addTo(this.map);
              const points = user.path.map(point => L.latLng(point.lat, point.lng));
              user.polyline.setLatLngs(points);
            }
          } else {
            // Hide track
            if (user.polyline) {
              this.map.removeLayer(user.polyline);
            }
          }
        }
      });
    },
    centerMapToFitAllUsers() {
      if (this.$refs.mapComponent) {
        this.$refs.mapComponent.centerMapToFitAllUsers();
      }
    },
    createPopupContent(userId, lat, lng, userName, altitude, speed, heading) {
      let content = `
        <div class="user-popup">
          <h4>${userName || `User ${userId}`}</h4>
          <div>Latitude: ${lat.toFixed(5)}</div>
          <div>Longitude: ${lng.toFixed(5)}</div>
      `;

      // Add optional details if available
      if (altitude !== null) {
        content += `<div>Altitude: ${altitude.toFixed(1)} m</div>`;
      }

      if (speed !== null) {
        content += `<div>Speed: ${speed.toFixed(1)} km/h</div>`;
      }

      if (heading !== null) {
        const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW', 'N'];
        const index = Math.round(heading / 45) % 8;
        content += `<div>Heading: ${heading.toFixed(0)}° (${directions[index]})</div>`;
      }

      content += '</div>';
      return content;
    },
    formatTimeSince(timestamp) {
      const now = Date.now();
      const secondsAgo = Math.floor((now - timestamp) / 1000);

      if (secondsAgo < 60) {
        return `${secondsAgo}s ago`;
      } else if (secondsAgo < 3600) {
        return `${Math.floor(secondsAgo / 60)}m ago`;
      } else if (secondsAgo < 86400) {
        return `${Math.floor(secondsAgo / 3600)}h ago`;
      } else {
        return `${Math.floor(secondsAgo / 86400)}d ago`;
      }
    },
    formatTimestamp(timestamp) {
      const date = new Date(timestamp);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${hours}:${minutes}:${seconds}`;
    },
    showUserHistory(userId) {
      this.selectedHistoryUserId = userId;
      this.showHistoryModal = true;
    },
    closeHistoryModal() {
      this.showHistoryModal = false;
      this.selectedHistoryUserId = null;
    },
    calculateDistance(lat1, lon1, lat2, lon2) {
      // Haversine formula to calculate distance between two points
      const R = 6371; // Radius of the Earth in km
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c; // Distance in km
      return distance;
    },
    deg2rad(deg) {
      return deg * (Math.PI / 180);
    },
    calculateTotalDistance(path) {
      if (!path || path.length < 2) return 0;

      let totalDistance = 0;
      for (let i = 1; i < path.length; i++) {
        totalDistance += this.calculateDistance(
          path[i - 1].lat, path[i - 1].lng,
          path[i].lat, path[i].lng
        );
      }

      return totalDistance;
    },
    calculateAverageSpeed(user) {
      if (!user || !user.path || user.path.length < 2) return 0;

      // Use the average of recorded speeds if available
      const speedPoints = user.path.filter(point => point.speed !== null && point.speed !== undefined);
      if (speedPoints.length > 0) {
        const totalSpeed = speedPoints.reduce((sum, point) => sum + point.speed, 0);
        return totalSpeed / speedPoints.length;
      }

      // Otherwise calculate based on total distance and time
      const totalDistance = this.calculateTotalDistance(user.path);
      const startTime = user.path[0].timestamp;
      const endTime = user.path[user.path.length - 1].timestamp;
      const durationHours = (endTime - startTime) / 3600000; // hours

      return durationHours > 0 ? totalDistance / durationHours : 0;
    },
    exportUserData(userId) {
      const user = this.trackedUsers[userId];
      if (!user) return;

      // Create exportable data
      const exportData = {
        userId: userId,
        userName: user.userName,
        trackPoints: user.path.map(point => ({
          latitude: point.lat,
          longitude: point.lng,
          timestamp: point.timestamp,
          altitude: point.altitude,
          speed: point.speed,
          heading: point.heading
        }))
      };

      // Convert to JSON string
      const dataStr = JSON.stringify(exportData, null, 2);

      // Create download link
      const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
      const exportFileName = `location-data-${userId}-${new Date().toISOString().slice(0, 10)}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileName);
      linkElement.click();
    },
    // Debug method to log tracked users
    debugTrackedUsers() {
      console.log("Current tracked users:", Object.keys(this.trackedUsers));
      console.log("trackedUsersCount:", this.trackedUsersCount);
    }
  },
  mounted() {
    this.connectWebSocket(); // Connect WebSocket

    // Debug: Log tracked users every few seconds
    // this.debugInterval = setInterval(() => {
    //   this.debugTrackedUsers();
    // }, 5000);
  },
  beforeDestroy() {
    // Clean up map instance
    if (this.map) {
      console.log('Removing map instance...');
      this.map.remove();
      this.map = null;
    }

    // Clean up user data to prevent memory leaks
    for (const userId in this.trackedUsers) {
      if (this.trackedUsers[userId].marker) this.map?.removeLayer(this.trackedUsers[userId].marker);
      if (this.trackedUsers[userId].polyline) this.map?.removeLayer(this.trackedUsers[userId].polyline);
    }

    // Remove socket listeners
    if (this.onlineChannel) {
      this.onlineChannel.off('user-location-update');
      this.onlineChannel.off('connect');
      this.onlineChannel.off('disconnect');
      this.onlineChannel.off('connect_error');
    }

    // Clear debug interval
    clearInterval(this.debugInterval);
  }
}
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 90vh;
  /* Adjust as needed */
  overflow: hidden;
}

/* Location Info */
.location-sending-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  z-index: 1000;
  max-width: calc(100% - 20px);
}

/* Responsive Styles */
@media (max-width: 768px) {

  /* Location Info */
  .location-sending-info {
    bottom: 60px;
    /* Adjust if needed based on other elements */
    font-size: 10px;
  }
}

/* Custom Marker Styles */
.custom-marker-container {
  background: transparent;
  border: none;
}

.custom-marker {
  transition: transform 0.2s ease;
}

.custom-marker:hover {
  transform: scale(1.2);
}

/* Leaflet Overrides */
.leaflet-popup-content {
  margin: 10px;
  line-height: 1.4;
}

.leaflet-popup-content-wrapper {
  border-radius: 4px;
}
</style>
