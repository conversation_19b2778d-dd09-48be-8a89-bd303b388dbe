<template>
  <div
    class="column-resizer"
    :class="resizerClasses"
    @mousedown="startResize"
    @touchstart="startResize"
  >
    <div class="resizer-handle">
      <div class="resizer-line"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ColumnResizer',
  
  props: {
    column: {
      type: Object,
      required: true
    },
    minWidth: {
      type: Number,
      default: 50
    },
    maxWidth: {
      type: Number,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showHandle: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      isResizing: false,
      startX: 0,
      startWidth: 0,
      currentWidth: 0
    }
  },

  computed: {
    /**
     * Resizer CSS classes
     */
    resizerClasses() {
      return [
        'column-resizer-vue2',
        {
          'column-resizer--active': this.isResizing,
          'column-resizer--disabled': this.disabled,
          'column-resizer--with-handle': this.showHandle
        }
      ]
    }
  },

  mounted() {
    // Add global event listeners
    document.addEventListener('mousemove', this.handleResize)
    document.addEventListener('mouseup', this.stopResize)
    document.addEventListener('touchmove', this.handleResize, { passive: false })
    document.addEventListener('touchend', this.stopResize)
  },

  beforeDestroy() {
    // Remove global event listeners
    document.removeEventListener('mousemove', this.handleResize)
    document.removeEventListener('mouseup', this.stopResize)
    document.removeEventListener('touchmove', this.handleResize)
    document.removeEventListener('touchend', this.stopResize)
  },

  methods: {
    /**
     * Start column resize
     */
    startResize(event) {
      if (this.disabled) return
      
      event.preventDefault()
      event.stopPropagation()
      
      this.isResizing = true
      
      // Get starting position
      const clientX = event.type === 'touchstart' 
        ? event.touches[0].clientX 
        : event.clientX
      
      this.startX = clientX
      
      // Get current column width
      const headerCell = this.$el.closest('th')
      if (headerCell) {
        this.startWidth = headerCell.offsetWidth
        this.currentWidth = this.startWidth
      }
      
      // Set cursor and prevent text selection
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
      document.body.style.webkitUserSelect = 'none'
      
      // Emit resize start event
      this.$emit('resize-start', {
        column: this.column,
        startWidth: this.startWidth,
        startX: this.startX
      })
    },

    /**
     * Handle column resize
     */
    handleResize(event) {
      if (!this.isResizing) return
      
      event.preventDefault()
      
      // Get current position
      const clientX = event.type === 'touchmove' 
        ? event.touches[0].clientX 
        : event.clientX
      
      // Calculate new width
      const deltaX = clientX - this.startX
      let newWidth = this.startWidth + deltaX
      
      // Apply constraints
      newWidth = Math.max(this.minWidth, newWidth)
      if (this.maxWidth) {
        newWidth = Math.min(this.maxWidth, newWidth)
      }
      
      this.currentWidth = newWidth
      
      // Emit resize event
      this.$emit('resize', {
        column: this.column,
        width: newWidth,
        deltaX: deltaX
      })
    },

    /**
     * Stop column resize
     */
    stopResize() {
      if (!this.isResizing) return
      
      this.isResizing = false
      
      // Reset cursor and text selection
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
      document.body.style.webkitUserSelect = ''
      
      // Emit resize end event
      this.$emit('resize-end', {
        column: this.column,
        finalWidth: this.currentWidth,
        startWidth: this.startWidth
      })
      
      // Reset state
      this.startX = 0
      this.startWidth = 0
      this.currentWidth = 0
    }
  }
}
</script>

<style lang="scss" scoped>
/* Column Resizer Styles */

.column-resizer {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 100%;
  cursor: col-resize;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  
  &:hover {
    opacity: 1;
  }
  
  &--active {
    opacity: 1;
  }
  
  &--disabled {
    cursor: not-allowed;
    opacity: 0.3;
    
    &:hover {
      opacity: 0.3;
    }
  }
  
  &--with-handle {
    .resizer-handle {
      opacity: 1;
    }
  }
}

.resizer-handle {
  width: 2px;
  height: 60%;
  background: #6366f1;
  border-radius: 1px;
  opacity: 0;
  transition: all 0.2s ease;
  position: relative;
  
  .column-resizer:hover & {
    opacity: 1;
    background: #5856eb;
  }
  
  .column-resizer--active & {
    opacity: 1;
    background: #4f46e5;
    height: 80%;
  }
}

.resizer-line {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1px;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0.5px;
}

/* Parent hover effect */
th:hover .column-resizer {
  opacity: 1;
}

/* Active resizing styles */
.column-resizer--active {
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    cursor: col-resize;
    pointer-events: none;
  }
}

/* Dark Theme */
.vue2-datatable--dark {
  .resizer-handle {
    background: #8b5cf6;
    
    .column-resizer:hover & {
      background: #7c3aed;
    }
    
    .column-resizer--active & {
      background: #6d28d9;
    }
  }
  
  .resizer-line {
    background: rgba(0, 0, 0, 0.3);
  }
}

/* Compact Theme */
.vue2-datatable--compact {
  .column-resizer {
    width: 6px;
  }
  
  .resizer-handle {
    width: 1px;
    height: 50%;
    
    .column-resizer--active & {
      height: 70%;
    }
  }
}

/* Touch Devices */
@media (hover: none) and (pointer: coarse) {
  .column-resizer {
    width: 12px;
    opacity: 0.7;
    
    &:active {
      opacity: 1;
    }
  }
  
  .resizer-handle {
    width: 3px;
    opacity: 1;
    background: #6366f1;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .resizer-handle {
    width: 1px;
  }
  
  .resizer-line {
    width: 0.5px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .column-resizer,
  .resizer-handle {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .column-resizer {
    display: none;
  }
}

/* Focus Styles for Accessibility */
.column-resizer:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
  opacity: 1;
}

.column-resizer:focus .resizer-handle {
  opacity: 1;
  background: #4f46e5;
}

/* Animation for smooth resizing */
@keyframes resizeIndicator {
  0% {
    transform: scaleY(0.8);
  }
  50% {
    transform: scaleY(1.2);
  }
  100% {
    transform: scaleY(1);
  }
}

.column-resizer--active .resizer-handle {
  animation: resizeIndicator 0.3s ease;
}

/* Hover effects for better UX */
.column-resizer::before {
  content: '';
  position: absolute;
  top: -4px;
  bottom: -4px;
  left: -4px;
  right: -4px;
  background: transparent;
  border-radius: 4px;
}

.column-resizer:hover::before {
  background: rgba(99, 102, 241, 0.1);
}

/* Visual feedback during resize */
.column-resizer--active::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 1px;
  background: #6366f1;
  box-shadow: 0 0 4px rgba(99, 102, 241, 0.5);
  transform: translateX(-50%);
}
</style>
