<template>
  <div class="search-container-vue2">
    <!-- Search Header -->
    <div class="search-header">
      <div class="search-label">
        <CIcon name="cil-filter" class="search-icon-header" />
        <span>Filter Data</span>
      </div>
      <div v-if="stats && stats.hasFilter" class="search-stats">
        <CIcon name="cil-check-circle" class="search-stats-icon" />
        <span class="results-count">{{ stats.filtered }}</span>
        <span class="results-text">found of</span>
        <span class="total-count">{{ stats.total }}</span>
        <span class="total-text">total</span>
        <span v-if="stats.duration" class="duration-text">({{ stats.duration.toFixed(1) }}ms)</span>
      </div>
    </div>

    <!-- Search Input -->
    <div class="search-input-wrapper">
      <CIcon name="cil-magnifying-glass" class="search-icon" />
      <input
        ref="searchInput"
        v-model="internalValue"
        type="text"
        class="search-input"
        :placeholder="placeholder"
        :disabled="loading"
        @input="handleInput"
        @keydown="handleKeydown"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      
      <!-- Loading Indicator -->
      <div v-if="loading" class="search-loading">
        <div class="loading-spinner"></div>
      </div>
      
      <!-- Clear Button -->
      <button
        v-if="internalValue"
        type="button"
        class="search-clear"
        @click="clearSearch"
        title="Clear search"
      >
        <CIcon name="cil-x" />
      </button>
    </div>

    <!-- Search Suggestions -->
    <div v-if="showSuggestions && suggestions.length > 0" class="search-suggestions">
      <div class="suggestions-header">
        <CIcon name="cil-lightbulb" class="suggestions-icon" />
        <span>Suggestions</span>
      </div>
      <div class="suggestions-list">
        <button
          v-for="(suggestion, index) in suggestions"
          :key="index"
          type="button"
          class="suggestion-item"
          :class="{ active: selectedSuggestionIndex === index }"
          @click="selectSuggestion(suggestion)"
          @mouseenter="selectedSuggestionIndex = index"
        >
          {{ suggestion }}
        </button>
      </div>
    </div>

    <!-- Advanced Filters (if enabled) -->
    <div v-if="showAdvancedFilters" class="advanced-filters">
      <div class="filters-header">
        <CIcon name="cil-settings" class="filters-icon" />
        <span>Advanced Filters</span>
        <button
          type="button"
          class="filters-toggle"
          @click="toggleAdvancedFilters"
        >
          <CIcon :name="filtersExpanded ? 'cil-chevron-top' : 'cil-chevron-bottom'" />
        </button>
      </div>
      
      <div v-if="filtersExpanded" class="filters-content">
        <!-- Filter controls will be added here -->
        <div class="filter-controls">
          <div class="search-mode-selector">
            <label class="search-mode-label">Search Mode:</label>
            <select v-model="searchMode" class="search-mode-select">
              <option value="contains">Contains</option>
              <option value="startsWith">Starts With</option>
              <option value="exact">Exact Match</option>
            </select>
          </div>
          
          <div class="search-options">
            <label class="search-option">
              <input
                v-model="caseSensitive"
                type="checkbox"
                class="search-checkbox"
              />
              <span>Case Sensitive</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Saved Searches -->
    <div v-if="showSavedSearches && savedSearches.length > 0" class="saved-searches">
      <div class="saved-searches-header">
        <CIcon name="cil-bookmark" class="saved-icon" />
        <span>Saved Searches</span>
      </div>
      <div class="saved-searches-list">
        <button
          v-for="search in savedSearches"
          :key="search.name"
          type="button"
          class="saved-search-item"
          @click="loadSearch(search)"
          :title="`Load search: ${search.name}`"
        >
          <span class="saved-search-name">{{ search.name }}</span>
          <span class="saved-search-term">{{ search.searchTerm }}</span>
          <button
            type="button"
            class="saved-search-delete"
            @click.stop="deleteSearch(search.name)"
            title="Delete saved search"
          >
            <CIcon name="cil-trash" />
          </button>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SearchBar',
  
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: 'Search in all fields...'
    },
    suggestions: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    stats: {
      type: Object,
      default: null
    },
    savedSearches: {
      type: Array,
      default: () => []
    },
    showAdvancedFilters: {
      type: Boolean,
      default: false
    },
    showSavedSearches: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      internalValue: this.value,
      showSuggestions: false,
      selectedSuggestionIndex: -1,
      filtersExpanded: false,
      searchMode: 'contains',
      caseSensitive: false,
      isFocused: false
    }
  },

  computed: {
    /**
     * Check if suggestions should be shown
     */
    shouldShowSuggestions() {
      return this.showSuggestions && 
             this.suggestions.length > 0 && 
             this.isFocused && 
             this.internalValue.length >= 2
    }
  },

  methods: {
    /**
     * Handle input change
     */
    handleInput(event) {
      this.internalValue = event.target.value
      this.$emit('input', this.internalValue)
      this.showSuggestions = this.internalValue.length >= 2
      this.selectedSuggestionIndex = -1
    },

    /**
     * Handle keydown events
     */
    handleKeydown(event) {
      if (!this.shouldShowSuggestions) return

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault()
          this.selectedSuggestionIndex = Math.min(
            this.selectedSuggestionIndex + 1,
            this.suggestions.length - 1
          )
          break
        case 'ArrowUp':
          event.preventDefault()
          this.selectedSuggestionIndex = Math.max(
            this.selectedSuggestionIndex - 1,
            -1
          )
          break
        case 'Enter':
          event.preventDefault()
          if (this.selectedSuggestionIndex >= 0) {
            this.selectSuggestion(this.suggestions[this.selectedSuggestionIndex])
          }
          break
        case 'Escape':
          this.showSuggestions = false
          this.selectedSuggestionIndex = -1
          this.$refs.searchInput.blur()
          break
      }
    },

    /**
     * Handle focus
     */
    handleFocus() {
      this.isFocused = true
      if (this.internalValue.length >= 2) {
        this.showSuggestions = true
      }
    },

    /**
     * Handle blur
     */
    handleBlur() {
      this.isFocused = false
      // Delay hiding suggestions to allow clicking
      setTimeout(() => {
        this.showSuggestions = false
        this.selectedSuggestionIndex = -1
      }, 200)
    },

    /**
     * Select a suggestion
     */
    selectSuggestion(suggestion) {
      this.internalValue = suggestion
      this.$emit('input', suggestion)
      this.showSuggestions = false
      this.selectedSuggestionIndex = -1
      this.$refs.searchInput.focus()
    },

    /**
     * Clear search
     */
    clearSearch() {
      this.internalValue = ''
      this.$emit('input', '')
      this.$emit('clear')
      this.showSuggestions = false
      this.$refs.searchInput.focus()
    },

    /**
     * Toggle advanced filters
     */
    toggleAdvancedFilters() {
      this.filtersExpanded = !this.filtersExpanded
    },

    /**
     * Load saved search
     */
    loadSearch(search) {
      this.$emit('load-search', search)
    },

    /**
     * Delete saved search
     */
    deleteSearch(name) {
      this.$emit('delete-search', name)
    },

    /**
     * Save current search
     */
    saveCurrentSearch() {
      if (!this.internalValue) return
      
      const name = prompt('Enter a name for this search:')
      if (name) {
        this.$emit('save-search', name)
      }
    }
  },

  watch: {
    value(newVal) {
      this.internalValue = newVal
    },

    searchMode(newVal) {
      this.$emit('search-mode-change', newVal)
    },

    caseSensitive(newVal) {
      this.$emit('case-sensitive-change', newVal)
    }
  }
}
</script>

<style lang="scss" scoped>
/* Search Bar Styles - Matching GenericDataTable Design */

.search-container-vue2 {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px 16px 0 0;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  position: relative;
}

/* Search Header */
.search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.search-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
  color: #374151;
}

.search-icon-header {
  width: 20px;
  height: 20px;
  color: #6366f1;
}

.search-stats {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
}

.search-stats-icon {
  width: 16px;
  height: 16px;
  color: #10b981;
}

.results-count {
  font-weight: 600;
  color: #10b981;
}

.total-count {
  font-weight: 600;
  color: #374151;
}

.duration-text {
  color: #9ca3af;
  font-size: 12px;
}

/* Search Input */
.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 16px;
  width: 18px;
  height: 18px;
  color: #9ca3af;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 14px 16px 14px 48px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  background: white;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
  
  &:disabled {
    background: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
  }
  
  &::placeholder {
    color: #9ca3af;
  }
}

.search-loading {
  position: absolute;
  right: 48px;
  display: flex;
  align-items: center;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.search-clear {
  position: absolute;
  right: 16px;
  width: 24px;
  height: 24px;
  border: none;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #e5e7eb;
  }
  
  svg {
    width: 12px;
    height: 12px;
    color: #6b7280;
  }
}

/* Search Suggestions */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 24px;
  right: 24px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.suggestions-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.suggestions-icon {
  width: 14px;
  height: 14px;
  color: #f59e0b;
}

.suggestions-list {
  max-height: 240px;
  overflow-y: auto;
}

.suggestion-item {
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover,
  &.active {
    background: #f8fafc;
  }
  
  &:last-child {
    border-radius: 0 0 8px 8px;
  }
}

/* Advanced Filters */
.advanced-filters {
  margin-top: 16px;
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.filters-header {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 0;
}

.filters-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.filters-toggle {
  margin-left: auto;
  border: none;
  background: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  
  &:hover {
    background: #f3f4f6;
  }
  
  svg {
    width: 16px;
    height: 16px;
    color: #6b7280;
  }
}

.filters-content {
  margin-top: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.filter-controls {
  display: flex;
  gap: 24px;
  align-items: center;
}

.search-mode-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-mode-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.search-mode-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  
  &:focus {
    outline: none;
    border-color: #6366f1;
  }
}

.search-options {
  display: flex;
  gap: 16px;
}

.search-option {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
}

.search-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #6366f1;
}

/* Saved Searches */
.saved-searches {
  margin-top: 16px;
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.saved-searches-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.saved-icon {
  width: 16px;
  height: 16px;
  color: #f59e0b;
}

.saved-searches-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.saved-search-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #6366f1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.saved-search-name {
  font-weight: 500;
  color: #374151;
  font-size: 12px;
}

.saved-search-term {
  color: #6b7280;
  font-size: 11px;
  font-style: italic;
}

.saved-search-delete {
  border: none;
  background: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  
  &:hover {
    background: #fee2e2;
  }
  
  svg {
    width: 12px;
    height: 12px;
    color: #dc2626;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-container-vue2 {
    padding: 16px 20px;
    border-radius: 8px 8px 0 0;
  }
  
  .search-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .search-stats {
    font-size: 12px;
  }
  
  .filter-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .saved-searches-list {
    flex-direction: column;
  }
  
  .saved-search-item {
    width: 100%;
  }
}
</style>
