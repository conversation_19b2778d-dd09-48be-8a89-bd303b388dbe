/**
 * Data formatting utilities for Vue2 DataTable
 * Provides common formatters for different data types
 */

/**
 * Format a number with thousands separators
 * @param {number|string} value - The value to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted number
 */
export function formatNumber(value, options = {}) {
  if (value === null || value === undefined || value === '') {
    return options.defaultValue || '-'
  }

  const num = parseFloat(value)
  if (isNaN(num)) {
    return options.defaultValue || '-'
  }

  const {
    decimals = 0,
    thousandsSeparator = ',',
    decimalSeparator = '.',
    prefix = '',
    suffix = ''
  } = options

  const formatted = num.toFixed(decimals)
    .replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator)
    .replace('.', decimalSeparator)

  return `${prefix}${formatted}${suffix}`
}

/**
 * Format a currency value
 * @param {number|string} value - The value to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted currency
 */
export function formatCurrency(value, options = {}) {
  const {
    currency = '$',
    decimals = 2,
    position = 'before'
  } = options

  const formatted = formatNumber(value, { decimals, ...options })
  
  if (formatted === '-') return formatted

  return position === 'before' ? `${currency}${formatted}` : `${formatted}${currency}`
}

/**
 * Format a percentage value
 * @param {number|string} value - The value to format (0-1 or 0-100)
 * @param {Object} options - Formatting options
 * @returns {string} Formatted percentage
 */
export function formatPercentage(value, options = {}) {
  const {
    decimals = 1,
    multiplier = 1,
    suffix = '%'
  } = options

  if (value === null || value === undefined || value === '') {
    return options.defaultValue || '-'
  }

  const num = parseFloat(value) * multiplier
  if (isNaN(num)) {
    return options.defaultValue || '-'
  }

  return `${num.toFixed(decimals)}${suffix}`
}

/**
 * Format a date value
 * @param {Date|string|number} value - The date value to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date
 */
export function formatDate(value, options = {}) {
  if (!value) {
    return options.defaultValue || '-'
  }

  const {
    format = 'YYYY-MM-DD',
    locale = 'en-US'
  } = options

  try {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return options.defaultValue || '-'
    }

    // Simple format patterns
    if (format === 'YYYY-MM-DD') {
      return date.toISOString().split('T')[0]
    } else if (format === 'DD/MM/YYYY') {
      return date.toLocaleDateString('en-GB')
    } else if (format === 'MM/DD/YYYY') {
      return date.toLocaleDateString('en-US')
    } else if (format === 'relative') {
      return formatRelativeDate(date)
    }

    // Use Intl.DateTimeFormat for complex formats
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(date)
  } catch (error) {
    console.warn('Date formatting error:', error)
    return options.defaultValue || '-'
  }
}

/**
 * Format a relative date (e.g., "2 days ago")
 * @param {Date} date - The date to format
 * @returns {string} Relative date string
 */
export function formatRelativeDate(date) {
  const now = new Date()
  const diffMs = now - date
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffMinutes = Math.floor(diffMs / (1000 * 60))

  if (diffDays > 7) {
    return formatDate(date, { format: 'YYYY-MM-DD' })
  } else if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
  } else if (diffMinutes > 0) {
    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`
  } else {
    return 'Just now'
  }
}

/**
 * Format a boolean value
 * @param {boolean|string|number} value - The value to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted boolean
 */
export function formatBoolean(value, options = {}) {
  const {
    trueText = 'Yes',
    falseText = 'No',
    nullText = '-'
  } = options

  if (value === null || value === undefined) {
    return nullText
  }

  // Handle string representations
  if (typeof value === 'string') {
    const lower = value.toLowerCase()
    if (lower === 'true' || lower === 'yes' || lower === '1') {
      return trueText
    } else if (lower === 'false' || lower === 'no' || lower === '0') {
      return falseText
    }
  }

  return value ? trueText : falseText
}

/**
 * Format file size in bytes to human readable format
 * @param {number} bytes - Size in bytes
 * @param {Object} options - Formatting options
 * @returns {string} Formatted file size
 */
export function formatFileSize(bytes, options = {}) {
  const {
    decimals = 1,
    binary = false
  } = options

  if (bytes === 0) return '0 Bytes'
  if (!bytes || bytes < 0) return '-'

  const k = binary ? 1024 : 1000
  const sizes = binary 
    ? ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB']
    : ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))
  const size = bytes / Math.pow(k, i)

  return `${size.toFixed(decimals)} ${sizes[i]}`
}

/**
 * Truncate text to specified length
 * @param {string} text - Text to truncate
 * @param {Object} options - Truncation options
 * @returns {string} Truncated text
 */
export function formatTruncate(text, options = {}) {
  const {
    length = 50,
    suffix = '...',
    wordBoundary = true
  } = options

  if (!text || text.length <= length) {
    return text || ''
  }

  let truncated = text.substring(0, length)

  if (wordBoundary) {
    const lastSpace = truncated.lastIndexOf(' ')
    if (lastSpace > 0) {
      truncated = truncated.substring(0, lastSpace)
    }
  }

  return truncated + suffix
}

/**
 * Format a status value with appropriate styling
 * @param {string} status - Status value
 * @param {Object} options - Formatting options
 * @returns {Object} Object with text and class
 */
export function formatStatus(status, options = {}) {
  const {
    statusMap = {
      active: { text: 'Active', class: 'status-active' },
      inactive: { text: 'Inactive', class: 'status-inactive' },
      pending: { text: 'Pending', class: 'status-pending' },
      completed: { text: 'Completed', class: 'status-completed' },
      cancelled: { text: 'Cancelled', class: 'status-cancelled' }
    },
    defaultStatus = { text: status, class: 'status-default' }
  } = options

  const normalizedStatus = status ? status.toLowerCase() : ''
  return statusMap[normalizedStatus] || defaultStatus
}

/**
 * Get column value from nested object path
 * @param {Object} item - Data item
 * @param {string} path - Dot notation path (e.g., 'user.profile.name')
 * @returns {any} Value at path
 */
export function getNestedValue(item, path) {
  if (!item || !path) return undefined

  return path.split('.').reduce((obj, key) => {
    return obj && obj[key] !== undefined ? obj[key] : undefined
  }, item)
}

/**
 * Apply formatter to column value
 * @param {any} value - Value to format
 * @param {Object} column - Column configuration
 * @param {Object} item - Full data item
 * @returns {any} Formatted value
 */
export function applyColumnFormatter(value, column, item) {
  if (!column.format) return value

  const { type, options = {} } = column.format

  switch (type) {
    case 'number':
      return formatNumber(value, options)
    case 'currency':
      return formatCurrency(value, options)
    case 'percentage':
      return formatPercentage(value, options)
    case 'date':
      return formatDate(value, options)
    case 'boolean':
      return formatBoolean(value, options)
    case 'fileSize':
      return formatFileSize(value, options)
    case 'truncate':
      return formatTruncate(value, options)
    case 'status':
      return formatStatus(value, options)
    case 'custom':
      return options.formatter ? options.formatter(value, item, column) : value
    default:
      return value
  }
}
