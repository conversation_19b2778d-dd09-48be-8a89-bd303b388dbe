// Vue2 CoreUI High-Performance DataTable - Main Export
// This is the entry point for the Vue2 DataTable component system

import Vue2DataTable from './components/core/Vue2DataTable.vue'
import TableHeader from './components/core/TableHeader.vue'
import TableBody from './components/core/TableBody.vue'
import TableRow from './components/core/TableRow.vue'
import SearchBar from './components/core/SearchBar.vue'
import Pagination from './components/core/Pagination.vue'
import LoadingSpinner from './components/core/LoadingSpinner.vue'
import EmptyState from './components/core/EmptyState.vue'
import ColumnResizer from './components/core/ColumnResizer.vue'

// Mixins
import VirtualScrollMixin from './mixins/VirtualScroll.js'
import DataManagerMixin from './mixins/DataManager.js'
import SearchMixin from './mixins/SearchMixin.js'
import SortingMixin from './mixins/SortingMixin.js'

// Utilities
import debounce from './utils/debounce.js'
import * as formatters from './utils/formatters.js'
import * as validators from './utils/validators.js'
import * as performance from './utils/performance.js'

// Main component export
export default Vue2DataTable

// Named exports for individual components
export {
  Vue2DataTable,
  TableHeader,
  TableBody,
  TableRow,
  SearchBar,
  Pagination,
  LoadingSpinner,
  EmptyState,
  ColumnResizer,
  VirtualScrollMixin,
  DataManagerMixin,
  SearchMixin,
  SortingMixin,
  debounce,
  formatters,
  validators,
  performance
}

// Vue plugin installation
export const Vue2DataTablePlugin = {
  install(Vue, options = {}) {
    // Register the main component globally
    Vue.component('Vue2DataTable', Vue2DataTable)
    
    // Register sub-components if needed
    if (options.registerSubComponents) {
      Vue.component('Vue2TableHeader', TableHeader)
      Vue.component('Vue2TableBody', TableBody)
      Vue.component('Vue2TableRow', TableRow)
      Vue.component('Vue2SearchBar', SearchBar)
      Vue.component('Vue2Pagination', Pagination)
      Vue.component('Vue2LoadingSpinner', LoadingSpinner)
      Vue.component('Vue2EmptyState', EmptyState)
      Vue.component('Vue2ColumnResizer', ColumnResizer)
    }
    
    // Add global configuration
    Vue.prototype.$vue2DataTableConfig = {
      defaultPageSize: 50,
      defaultRowHeight: 40,
      defaultBufferSize: 10,
      debounceDelay: 300,
      virtualScrollThreshold: 1000,
      performanceMonitoring: false,
      ...options
    }
  }
}

// Auto-install when used with script tag
if (typeof window !== 'undefined' && window.Vue) {
  window.Vue.use(Vue2DataTablePlugin)
}
