/**
 * Virtual Scrolling Mixin for Vue2 DataTable
 * Provides virtual scrolling functionality for handling large datasets
 */

import { throttlePerformance, createVirtualListCalculator } from '../utils/performance.js'

export default {
  data() {
    return {
      // Virtual scrolling state
      containerHeight: 600,
      containerWidth: 0,
      scrollTop: 0,
      scrollLeft: 0,
      
      // Visible range
      startIndex: 0,
      endIndex: 0,
      visibleRowCount: 0,
      
      // Buffer and optimization
      overscan: 3,
      
      // Column virtualization
      columnVirtualizationEnabled: false,
      visibleColumnStartIndex: 0,
      visibleColumnEndIndex: 0,
      totalColumnsWidth: 0,
      
      // Performance tracking
      lastScrollTime: 0,
      scrollDirection: 'down',
      isScrolling: false,
      scrollTimeout: null,
      
      // Resize observer
      resizeObserver: null,
      
      // Virtual list calculator
      virtualListCalculator: null
    }
  },

  computed: {
    /**
     * Check if virtual scrolling should be enabled
     */
    shouldUseVirtualScrolling() {
      if (this.virtualScrollDisabled) return false

      // If explicitly enabled, use virtual scrolling
      if (this.virtualScrollEnabled) return true

      // If explicitly disabled, don't use virtual scrolling
      if (this.virtualScrollEnabled === false) return false

      // Auto-enable based on threshold only if not explicitly set
      const threshold = this.virtualScrollThreshold || 1000
      const itemCount = this.filteredItems ? this.filteredItems.length : 0

      return itemCount >= threshold
    },

    /**
     * Check if column virtualization should be enabled
     */
    shouldUseColumnVirtualization() {
      if (!this.shouldUseVirtualScrolling) return false
      
      const threshold = this.columnVirtualizationThreshold || 50
      const columnCount = this.processedColumns ? this.processedColumns.length : 0
      
      return this.columnVirtualizationEnabled || columnCount >= threshold
    },

    /**
     * Get visible items for virtual scrolling
     */
    visibleItems() {
      if (!this.shouldUseVirtualScrolling) {
        return this.paginatedItems || this.filteredItems || []
      }

      const items = this.filteredItems || []
      return items.slice(this.startIndex, this.endIndex + 1)
    },

    /**
     * Get visible columns for column virtualization
     */
    visibleColumns() {
      if (!this.shouldUseColumnVirtualization) {
        return this.processedColumns || []
      }

      const columns = this.processedColumns || []
      return columns.slice(this.visibleColumnStartIndex, this.visibleColumnEndIndex + 1)
    },

    /**
     * Calculate top spacer height for virtual scrolling
     */
    topSpacerHeight() {
      if (!this.shouldUseVirtualScrolling) return 0
      return this.startIndex * this.rowHeight
    },

    /**
     * Calculate bottom spacer height for virtual scrolling
     */
    bottomSpacerHeight() {
      if (!this.shouldUseVirtualScrolling) return 0
      
      const totalItems = this.filteredItems ? this.filteredItems.length : 0
      const remainingItems = totalItems - (this.endIndex + 1)
      return Math.max(0, remainingItems * this.rowHeight)
    },

    /**
     * Calculate total virtual height
     */
    totalVirtualHeight() {
      if (!this.shouldUseVirtualScrolling) return 'auto'
      
      const totalItems = this.filteredItems ? this.filteredItems.length : 0
      return totalItems * this.rowHeight
    },

    /**
     * Calculate left spacer width for column virtualization
     */
    leftSpacerWidth() {
      if (!this.shouldUseColumnVirtualization) return 0
      
      let width = 0
      for (let i = 0; i < this.visibleColumnStartIndex; i++) {
        const column = this.processedColumns[i]
        width += this.getColumnWidth(column)
      }
      return width
    },

    /**
     * Calculate right spacer width for column virtualization
     */
    rightSpacerWidth() {
      if (!this.shouldUseColumnVirtualization) return 0
      
      let width = 0
      const columns = this.processedColumns || []
      for (let i = this.visibleColumnEndIndex + 1; i < columns.length; i++) {
        const column = columns[i]
        width += this.getColumnWidth(column)
      }
      return width
    }
  },

  mounted() {
    this.initializeVirtualScrolling()
  },

  beforeDestroy() {
    this.cleanupVirtualScrolling()
  },

  methods: {
    /**
     * Initialize virtual scrolling
     */
    initializeVirtualScrolling() {
      this.virtualListCalculator = createVirtualListCalculator({
        itemHeight: this.rowHeight,
        containerHeight: this.containerHeight,
        bufferSize: this.bufferSize,
        overscan: this.overscan
      })

      this.setupResizeObserver()
      this.updateContainerDimensions()
      this.calculateVisibleRange()
      this.calculateVisibleColumns()
    },

    /**
     * Setup resize observer for container
     */
    setupResizeObserver() {
      if (typeof ResizeObserver !== 'undefined' && this.$refs.tableContainer) {
        this.resizeObserver = new ResizeObserver(
          throttlePerformance(this.handleContainerResize.bind(this), 100)
        )
        this.resizeObserver.observe(this.$refs.tableContainer)
      }
    },

    /**
     * Handle container resize
     */
    handleContainerResize(entries) {
      if (entries && entries.length > 0) {
        const entry = entries[0]
        this.containerHeight = entry.contentRect.height
        this.containerWidth = entry.contentRect.width
        this.calculateVisibleRange()
        this.calculateVisibleColumns()
      }
    },

    /**
     * Update container dimensions
     */
    updateContainerDimensions() {
      if (this.$refs.tableContainer) {
        const rect = this.$refs.tableContainer.getBoundingClientRect()
        this.containerHeight = rect.height || 600
        this.containerWidth = rect.width || 0
      }
    },

    /**
     * Handle scroll event
     */
    handleScroll(event) {
      if (!this.shouldUseVirtualScrolling) return

      const target = event.target
      const newScrollTop = target.scrollTop
      const newScrollLeft = target.scrollLeft

      // Determine scroll direction
      this.scrollDirection = newScrollTop > this.scrollTop ? 'down' : 'up'
      
      this.scrollTop = newScrollTop
      this.scrollLeft = newScrollLeft
      this.lastScrollTime = Date.now()
      this.isScrolling = true

      // Clear existing timeout
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout)
      }

      // Throttled scroll handling
      this.throttledScrollHandler()

      // Set scroll end timeout
      this.scrollTimeout = setTimeout(() => {
        this.isScrolling = false
        this.onScrollEnd()
      }, 150)
    },

    /**
     * Throttled scroll handler
     */
    throttledScrollHandler: throttlePerformance(function() {
      this.calculateVisibleRange()
      this.calculateVisibleColumns()
      this.$emit('scroll', {
        scrollTop: this.scrollTop,
        scrollLeft: this.scrollLeft,
        direction: this.scrollDirection
      })
    }, 16),

    /**
     * Handle scroll end
     */
    onScrollEnd() {
      this.$emit('scroll-end', {
        scrollTop: this.scrollTop,
        scrollLeft: this.scrollLeft
      })
    },

    /**
     * Calculate visible row range
     */
    calculateVisibleRange() {
      if (!this.shouldUseVirtualScrolling || !this.virtualListCalculator) {
        this.startIndex = 0
        this.endIndex = this.filteredItems ? this.filteredItems.length - 1 : 0
        return
      }

      const itemCount = this.filteredItems ? this.filteredItems.length : 0
      if (itemCount === 0) {
        this.startIndex = 0
        this.endIndex = 0
        return
      }

      const range = this.virtualListCalculator.getVisibleRange(this.scrollTop, itemCount)
      this.startIndex = range.startIndex
      this.endIndex = range.endIndex
      this.visibleRowCount = this.endIndex - this.startIndex + 1
    },

    /**
     * Calculate visible column range
     */
    calculateVisibleColumns() {
      if (!this.shouldUseColumnVirtualization) {
        this.visibleColumnStartIndex = 0
        this.visibleColumnEndIndex = this.processedColumns ? this.processedColumns.length - 1 : 0
        return
      }

      const columns = this.processedColumns || []
      if (columns.length === 0) {
        this.visibleColumnStartIndex = 0
        this.visibleColumnEndIndex = 0
        return
      }

      let currentWidth = 0
      let startIndex = 0
      let endIndex = 0

      // Find start index
      for (let i = 0; i < columns.length; i++) {
        const columnWidth = this.getColumnWidth(columns[i])
        if (currentWidth + columnWidth > this.scrollLeft) {
          startIndex = Math.max(0, i - 1)
          break
        }
        currentWidth += columnWidth
      }

      // Find end index
      currentWidth = 0
      for (let i = startIndex; i < columns.length; i++) {
        const columnWidth = this.getColumnWidth(columns[i])
        currentWidth += columnWidth
        if (currentWidth >= this.containerWidth + 200) { // 200px buffer
          endIndex = i
          break
        }
        endIndex = i
      }

      this.visibleColumnStartIndex = startIndex
      this.visibleColumnEndIndex = Math.min(endIndex, columns.length - 1)
    },

    /**
     * Get column width
     */
    getColumnWidth(column) {
      if (column.width) {
        if (typeof column.width === 'number') {
          return column.width
        }
        if (typeof column.width === 'string') {
          const match = column.width.match(/(\d+)/)
          return match ? parseInt(match[1]) : this.columnWidth || 150
        }
      }
      return this.columnWidth || 150
    },

    /**
     * Scroll to specific row
     */
    scrollToRow(index) {
      if (!this.shouldUseVirtualScrolling) return

      const targetScrollTop = index * this.rowHeight
      if (this.$refs.tableContainer) {
        this.$refs.tableContainer.scrollTop = targetScrollTop
      }
    },

    /**
     * Scroll to specific column
     */
    scrollToColumn(index) {
      if (!this.shouldUseColumnVirtualization) return

      let targetScrollLeft = 0
      const columns = this.processedColumns || []
      
      for (let i = 0; i < index && i < columns.length; i++) {
        targetScrollLeft += this.getColumnWidth(columns[i])
      }

      if (this.$refs.tableContainer) {
        this.$refs.tableContainer.scrollLeft = targetScrollLeft
      }
    },

    /**
     * Get row offset for virtual positioning
     */
    getRowOffset(index) {
      return (this.startIndex + index) * this.rowHeight
    },

    /**
     * Check if row is visible
     */
    isRowVisible(index) {
      return index >= this.startIndex && index <= this.endIndex
    },

    /**
     * Check if column is visible
     */
    isColumnVisible(index) {
      return index >= this.visibleColumnStartIndex && index <= this.visibleColumnEndIndex
    },

    /**
     * Cleanup virtual scrolling
     */
    cleanupVirtualScrolling() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
        this.resizeObserver = null
      }

      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout)
        this.scrollTimeout = null
      }
    },

    /**
     * Force recalculation of virtual scrolling
     */
    recalculateVirtualScrolling() {
      this.$nextTick(() => {
        this.updateContainerDimensions()
        this.calculateVisibleRange()
        this.calculateVisibleColumns()
      })
    }
  },

  watch: {
    filteredItems: {
      handler() {
        this.calculateVisibleRange()
      },
      immediate: true
    },

    processedColumns: {
      handler() {
        this.calculateVisibleColumns()
      },
      immediate: true
    },

    rowHeight() {
      this.recalculateVirtualScrolling()
    },

    columnWidth() {
      this.calculateVisibleColumns()
    }
  }
}
