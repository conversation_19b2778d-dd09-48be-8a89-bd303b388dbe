/**
 * Sorting Mixin for Vue2 DataTable
 * Provides comprehensive sorting functionality with multi-column support
 */

import { globalPerformanceMonitor } from '../utils/performance.js'

export default {
  data() {
    return {
      // Sorting state
      sortColumn: null,
      sortDirection: 'asc',
      multiSort: false,
      sortStack: [], // For multi-column sorting
      
      // Sorting configuration
      sortDirections: ['asc', 'desc'],
      defaultSortDirection: 'asc',
      sortNullsLast: true,
      caseSensitiveSort: false,
      
      // Custom sort functions
      customSortFunctions: {},
      
      // Sort performance
      sortDuration: 0,
      lastSortTime: 0
    }
  },

  computed: {
    /**
     * Get sorted items
     */
    sortedItems() {
      const endMeasurement = globalPerformanceMonitor.startMeasurement('sortItems')
      
      try {
        const items = this.filteredItems || this.items || []
        
        if (!this.sortColumn && this.sortStack.length === 0) {
          return items
        }

        return this.performSort([...items])
      } finally {
        const result = endMeasurement()
        this.sortDuration = result.duration
      }
    },

    /**
     * Get current sort state
     */
    sortState() {
      return {
        column: this.sortColumn,
        direction: this.sortDirection,
        multiSort: this.multiSort,
        sortStack: [...this.sortStack],
        duration: this.sortDuration
      }
    },

    /**
     * Check if multi-sort is active
     */
    hasMultiSort() {
      return this.multiSort && this.sortStack.length > 1
    },

    /**
     * Get sort indicators for columns
     */
    sortIndicators() {
      const indicators = {}
      
      // Single sort
      if (this.sortColumn && !this.multiSort) {
        indicators[this.sortColumn] = {
          direction: this.sortDirection,
          priority: 1,
          active: true
        }
      }
      
      // Multi sort
      if (this.multiSort && this.sortStack.length > 0) {
        this.sortStack.forEach((sort, index) => {
          indicators[sort.column] = {
            direction: sort.direction,
            priority: index + 1,
            active: true
          }
        })
      }
      
      return indicators
    }
  },

  methods: {
    /**
     * Sort by column
     */
    sortBy(column, direction = null, addToStack = false) {
      if (!column || !this.isColumnSortable(column)) {
        return
      }

      this.lastSortTime = performance.now()

      if (this.multiSort && (addToStack || this.hasMultiSort)) {
        this.handleMultiSort(column, direction)
      } else {
        this.handleSingleSort(column, direction)
      }

      this.$emit('sort', {
        column,
        direction: this.sortDirection,
        multiSort: this.multiSort,
        sortStack: [...this.sortStack]
      })
    },

    /**
     * Handle single column sorting
     */
    handleSingleSort(column, direction) {
      if (this.sortColumn === column && !direction) {
        // Cycle through sort directions
        const currentIndex = this.sortDirections.indexOf(this.sortDirection)
        const nextIndex = (currentIndex + 1) % (this.sortDirections.length + 1)
        
        if (nextIndex === this.sortDirections.length) {
          // Clear sort
          this.clearSort()
          return
        } else {
          this.sortDirection = this.sortDirections[nextIndex]
        }
      } else {
        this.sortColumn = column
        this.sortDirection = direction || this.defaultSortDirection
      }

      // Clear multi-sort stack when doing single sort
      this.sortStack = []
    },

    /**
     * Handle multi-column sorting
     */
    handleMultiSort(column, direction) {
      const existingIndex = this.sortStack.findIndex(sort => sort.column === column)
      
      if (existingIndex > -1) {
        // Update existing sort
        const existingSort = this.sortStack[existingIndex]
        
        if (!direction) {
          // Cycle through directions
          const currentIndex = this.sortDirections.indexOf(existingSort.direction)
          const nextIndex = (currentIndex + 1) % (this.sortDirections.length + 1)
          
          if (nextIndex === this.sortDirections.length) {
            // Remove from stack
            this.sortStack.splice(existingIndex, 1)
          } else {
            existingSort.direction = this.sortDirections[nextIndex]
          }
        } else {
          existingSort.direction = direction
        }
      } else {
        // Add new sort
        this.sortStack.push({
          column,
          direction: direction || this.defaultSortDirection
        })
      }

      // Update primary sort from stack
      if (this.sortStack.length > 0) {
        const primarySort = this.sortStack[0]
        this.sortColumn = primarySort.column
        this.sortDirection = primarySort.direction
      } else {
        this.clearSort()
      }
    },

    /**
     * Perform the actual sorting
     */
    performSort(items) {
      if (this.multiSort && this.sortStack.length > 0) {
        return this.performMultiSort(items)
      } else if (this.sortColumn) {
        return this.performSingleSort(items)
      }
      
      return items
    },

    /**
     * Perform single column sort
     */
    performSingleSort(items) {
      const column = this.getColumnConfig(this.sortColumn)
      const sortFunction = this.getSortFunction(column)
      
      return items.sort((a, b) => {
        const result = sortFunction(a, b, column)
        return this.sortDirection === 'desc' ? -result : result
      })
    },

    /**
     * Perform multi-column sort
     */
    performMultiSort(items) {
      return items.sort((a, b) => {
        for (const sort of this.sortStack) {
          const column = this.getColumnConfig(sort.column)
          const sortFunction = this.getSortFunction(column)
          const result = sortFunction(a, b, column)
          
          if (result !== 0) {
            return sort.direction === 'desc' ? -result : result
          }
        }
        return 0
      })
    },

    /**
     * Get sort function for column
     */
    getSortFunction(column) {
      // Custom sort function
      if (column.sortFunction && typeof column.sortFunction === 'function') {
        return column.sortFunction
      }

      // Registered custom sort function
      if (column.sortType && this.customSortFunctions[column.sortType]) {
        return this.customSortFunctions[column.sortType]
      }

      // Default sort function based on column type
      switch (column.type) {
        case 'number':
          return this.numberSort
        case 'date':
          return this.dateSort
        case 'boolean':
          return this.booleanSort
        case 'string':
        default:
          return this.stringSort
      }
    },

    /**
     * String sort function
     */
    stringSort(a, b, column) {
      const valueA = this.getSortValue(a, column)
      const valueB = this.getSortValue(b, column)
      
      if (this.isNullOrUndefined(valueA) && this.isNullOrUndefined(valueB)) return 0
      if (this.isNullOrUndefined(valueA)) return this.sortNullsLast ? 1 : -1
      if (this.isNullOrUndefined(valueB)) return this.sortNullsLast ? -1 : 1
      
      const strA = this.caseSensitiveSort ? String(valueA) : String(valueA).toLowerCase()
      const strB = this.caseSensitiveSort ? String(valueB) : String(valueB).toLowerCase()
      
      return strA.localeCompare(strB)
    },

    /**
     * Number sort function
     */
    numberSort(a, b, column) {
      const valueA = this.getSortValue(a, column)
      const valueB = this.getSortValue(b, column)
      
      if (this.isNullOrUndefined(valueA) && this.isNullOrUndefined(valueB)) return 0
      if (this.isNullOrUndefined(valueA)) return this.sortNullsLast ? 1 : -1
      if (this.isNullOrUndefined(valueB)) return this.sortNullsLast ? -1 : 1
      
      const numA = Number(valueA)
      const numB = Number(valueB)
      
      if (isNaN(numA) && isNaN(numB)) return 0
      if (isNaN(numA)) return this.sortNullsLast ? 1 : -1
      if (isNaN(numB)) return this.sortNullsLast ? -1 : 1
      
      return numA - numB
    },

    /**
     * Date sort function
     */
    dateSort(a, b, column) {
      const valueA = this.getSortValue(a, column)
      const valueB = this.getSortValue(b, column)
      
      if (this.isNullOrUndefined(valueA) && this.isNullOrUndefined(valueB)) return 0
      if (this.isNullOrUndefined(valueA)) return this.sortNullsLast ? 1 : -1
      if (this.isNullOrUndefined(valueB)) return this.sortNullsLast ? -1 : 1
      
      const dateA = new Date(valueA)
      const dateB = new Date(valueB)
      
      if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) return 0
      if (isNaN(dateA.getTime())) return this.sortNullsLast ? 1 : -1
      if (isNaN(dateB.getTime())) return this.sortNullsLast ? -1 : 1
      
      return dateA.getTime() - dateB.getTime()
    },

    /**
     * Boolean sort function
     */
    booleanSort(a, b, column) {
      const valueA = this.getSortValue(a, column)
      const valueB = this.getSortValue(b, column)
      
      if (this.isNullOrUndefined(valueA) && this.isNullOrUndefined(valueB)) return 0
      if (this.isNullOrUndefined(valueA)) return this.sortNullsLast ? 1 : -1
      if (this.isNullOrUndefined(valueB)) return this.sortNullsLast ? -1 : 1
      
      const boolA = Boolean(valueA)
      const boolB = Boolean(valueB)
      
      return boolA === boolB ? 0 : (boolA ? 1 : -1)
    },

    /**
     * Get sort value from item
     */
    getSortValue(item, column) {
      if (column.sortKey) {
        return this.getNestedValue(item, column.sortKey)
      }
      
      return this.getNestedValue(item, column.key)
    },

    /**
     * Get nested value from object
     */
    getNestedValue(obj, path) {
      return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : undefined
      }, obj)
    },

    /**
     * Check if value is null or undefined
     */
    isNullOrUndefined(value) {
      return value === null || value === undefined || value === ''
    },

    /**
     * Get column configuration
     */
    getColumnConfig(columnKey) {
      const columns = this.processedColumns || this.columns || []
      return columns.find(col => col.key === columnKey) || { key: columnKey }
    },

    /**
     * Check if column is sortable
     */
    isColumnSortable(columnKey) {
      const column = this.getColumnConfig(columnKey)
      return column.sortable !== false
    },

    /**
     * Clear all sorting
     */
    clearSort() {
      this.sortColumn = null
      this.sortDirection = this.defaultSortDirection
      this.sortStack = []
      
      this.$emit('sort-clear')
    },

    /**
     * Toggle multi-sort mode
     */
    toggleMultiSort() {
      this.multiSort = !this.multiSort
      
      if (!this.multiSort) {
        // Convert current sort to single sort
        this.sortStack = []
      } else if (this.sortColumn) {
        // Convert current sort to multi-sort
        this.sortStack = [{
          column: this.sortColumn,
          direction: this.sortDirection
        }]
      }
      
      this.$emit('multi-sort-toggle', this.multiSort)
    },

    /**
     * Register custom sort function
     */
    registerSortFunction(name, sortFunction) {
      if (typeof sortFunction === 'function') {
        this.customSortFunctions[name] = sortFunction
      }
    },

    /**
     * Get sort direction for column
     */
    getSortDirection(columnKey) {
      const indicator = this.sortIndicators[columnKey]
      return indicator ? indicator.direction : null
    },

    /**
     * Check if column is currently sorted
     */
    isColumnSorted(columnKey) {
      return !!this.sortIndicators[columnKey]
    },

    /**
     * Get sort priority for column (in multi-sort)
     */
    getSortPriority(columnKey) {
      const indicator = this.sortIndicators[columnKey]
      return indicator ? indicator.priority : null
    }
  },

  watch: {
    sortColumn() {
      this.$emit('sort-change', this.sortState)
    },

    sortDirection() {
      this.$emit('sort-change', this.sortState)
    },

    sortStack: {
      handler() {
        this.$emit('sort-change', this.sortState)
      },
      deep: true
    }
  }
}
