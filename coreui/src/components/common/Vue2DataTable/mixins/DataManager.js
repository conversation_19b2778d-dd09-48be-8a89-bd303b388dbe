/**
 * Data Manager Mixin for Vue2 DataTable
 * Handles data fetching, caching, and state management
 */

import { globalPerformanceMonitor } from '../utils/performance.js'

export default {
  data() {
    return {
      // Data state
      internalData: [],
      loading: false,
      error: null,
      
      // Cache management
      dataCache: new Map(),
      cacheTimeout: 5 * 60 * 1000, // 5 minutes
      maxCacheSize: 100,
      
      // Request management
      currentRequest: null,
      requestQueue: [],
      retryCount: 0,
      maxRetries: 3,
      
      // Pagination state
      totalRows: 0,
      serverSidePagination: false,
      
      // Loading states
      initialLoading: true,
      refreshing: false,
      loadingMore: false,
      
      // Error handling
      lastError: null,
      errorRetryDelay: 1000,
      
      // Performance tracking
      lastFetchTime: 0,
      fetchDuration: 0
    }
  },

  computed: {
    /**
     * Get current data items
     */
    items() {
      return this.internalData || []
    },

    /**
     * Check if data source is a URL
     */
    isUrlDataSource() {
      return typeof this.dataSource === 'string'
    },

    /**
     * Check if data source is a function
     */
    isFunctionDataSource() {
      return typeof this.dataSource === 'function'
    },

    /**
     * Check if data source is an array
     */
    isArrayDataSource() {
      return Array.isArray(this.dataSource)
    },

    /**
     * Get cache key for current request
     */
    cacheKey() {
      if (this.isArrayDataSource) return null
      
      const params = {
        dataSource: this.dataSource,
        page: this.currentPage,
        pageSize: this.pageSize,
        searchTerm: this.searchTerm,
        sortColumn: this.sortColumn,
        sortDirection: this.sortDirection,
        filters: this.activeFilters
      }
      
      return JSON.stringify(params)
    },

    /**
     * Check if current request can be cached
     */
    canUseCache() {
      if (!this.cacheKey) return false
      
      const cached = this.dataCache.get(this.cacheKey)
      if (!cached) return false
      
      const now = Date.now()
      return (now - cached.timestamp) < this.cacheTimeout
    },

    /**
     * Get loading state information
     */
    loadingState() {
      return {
        loading: this.loading,
        initialLoading: this.initialLoading,
        refreshing: this.refreshing,
        loadingMore: this.loadingMore,
        error: this.error,
        retryCount: this.retryCount
      }
    }
  },

  created() {
    this.initializeDataManager()
  },

  beforeDestroy() {
    this.cleanupDataManager()
  },

  methods: {
    /**
     * Initialize data manager
     */
    initializeDataManager() {
      this.loadData()
    },

    /**
     * Load data based on data source type
     */
    async loadData(options = {}) {
      const endMeasurement = globalPerformanceMonitor.startMeasurement('loadData')
      
      try {
        this.setLoadingState(true, options)
        this.error = null

        let data
        if (this.isArrayDataSource) {
          data = await this.loadArrayData()
        } else if (this.isUrlDataSource) {
          data = await this.loadUrlData()
        } else if (this.isFunctionDataSource) {
          data = await this.loadFunctionData()
        } else {
          throw new Error('Invalid data source type')
        }

        this.processLoadedData(data)
        this.setLoadingState(false)
        
        this.$emit('data-loaded', {
          data: this.internalData,
          totalRows: this.totalRows,
          duration: this.fetchDuration
        })

      } catch (error) {
        this.handleLoadError(error)
      } finally {
        const result = endMeasurement()
        this.fetchDuration = result.duration
      }
    },

    /**
     * Load array data
     */
    async loadArrayData() {
      return {
        data: [...this.dataSource],
        total: this.dataSource.length
      }
    },

    /**
     * Load data from URL
     */
    async loadUrlData() {
      // Check cache first
      if (this.canUseCache) {
        const cached = this.dataCache.get(this.cacheKey)
        return cached.data
      }

      const params = this.buildRequestParams()
      const url = this.buildRequestUrl(params)
      
      // Cancel previous request
      if (this.currentRequest) {
        this.currentRequest.cancel()
      }

      // Create new request
      this.currentRequest = this.createCancelableRequest(url, params)
      
      try {
        const response = await this.currentRequest.promise
        const data = this.parseResponse(response)
        
        // Cache the result
        this.cacheData(data)
        
        return data
      } finally {
        this.currentRequest = null
      }
    },

    /**
     * Load data using function
     */
    async loadFunctionData() {
      const params = this.buildRequestParams()
      const result = await this.dataSource(params)
      
      if (result && typeof result.then === 'function') {
        return await result
      }
      
      return result
    },

    /**
     * Build request parameters
     */
    buildRequestParams() {
      return {
        page: this.currentPage || 1,
        limit: this.pageSize || 50,
        offset: ((this.currentPage || 1) - 1) * (this.pageSize || 50),
        search: this.searchTerm || '',
        sortBy: this.sortColumn || '',
        sortOrder: this.sortDirection || 'asc',
        filters: this.activeFilters || {}
      }
    },

    /**
     * Build request URL with parameters
     */
    buildRequestUrl(params) {
      const url = new URL(this.dataSource)
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          if (typeof value === 'object') {
            url.searchParams.set(key, JSON.stringify(value))
          } else {
            url.searchParams.set(key, String(value))
          }
        }
      })
      
      return url.toString()
    },

    /**
     * Create cancelable HTTP request
     */
    createCancelableRequest(url, params) {
      const controller = new AbortController()
      
      const promise = fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...this.requestHeaders
        },
        signal: controller.signal,
        ...this.requestOptions
      }).then(response => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        return response.json()
      })

      return {
        promise,
        cancel: () => controller.abort()
      }
    },

    /**
     * Parse API response
     */
    parseResponse(response) {
      // Handle different response formats
      if (Array.isArray(response)) {
        return {
          data: response,
          total: response.length
        }
      }

      if (response.data && Array.isArray(response.data)) {
        return {
          data: response.data,
          total: response.total || response.count || response.data.length
        }
      }

      if (response.items && Array.isArray(response.items)) {
        return {
          data: response.items,
          total: response.total || response.count || response.items.length
        }
      }

      throw new Error('Invalid response format')
    },

    /**
     * Process loaded data
     */
    processLoadedData(result) {
      this.internalData = result.data || []
      this.totalRows = result.total || this.internalData.length
      
      // Freeze data for performance if enabled
      if (this.freezeData) {
        this.internalData = Object.freeze(this.internalData.map(item => Object.freeze(item)))
      }
      
      this.initialLoading = false
      this.lastFetchTime = Date.now()
    },

    /**
     * Cache data result
     */
    cacheData(data) {
      if (!this.cacheKey) return
      
      // Clean old cache entries if needed
      if (this.dataCache.size >= this.maxCacheSize) {
        const oldestKey = this.dataCache.keys().next().value
        this.dataCache.delete(oldestKey)
      }
      
      this.dataCache.set(this.cacheKey, {
        data,
        timestamp: Date.now()
      })
    },

    /**
     * Set loading state
     */
    setLoadingState(loading, options = {}) {
      this.loading = loading
      
      if (options.refresh) {
        this.refreshing = loading
      }
      
      if (options.loadMore) {
        this.loadingMore = loading
      }
      
      this.$emit('loading-change', this.loadingState)
    },

    /**
     * Handle load error
     */
    handleLoadError(error) {
      this.error = error
      this.lastError = error
      this.setLoadingState(false)
      
      console.error('Vue2DataTable: Data loading error:', error)
      
      this.$emit('error', {
        error,
        retryCount: this.retryCount,
        canRetry: this.retryCount < this.maxRetries
      })
      
      // Auto retry if enabled
      if (this.autoRetry && this.retryCount < this.maxRetries) {
        this.scheduleRetry()
      }
    },

    /**
     * Schedule retry attempt
     */
    scheduleRetry() {
      const delay = this.errorRetryDelay * Math.pow(2, this.retryCount)
      
      setTimeout(() => {
        this.retryCount++
        this.loadData({ retry: true })
      }, delay)
    },

    /**
     * Refresh data
     */
    async refresh() {
      this.clearCache()
      await this.loadData({ refresh: true })
    },

    /**
     * Reload data (alias for refresh)
     */
    async reload() {
      return this.refresh()
    },

    /**
     * Clear data cache
     */
    clearCache() {
      this.dataCache.clear()
    },

    /**
     * Reset error state
     */
    resetError() {
      this.error = null
      this.lastError = null
      this.retryCount = 0
    },

    /**
     * Cleanup data manager
     */
    cleanupDataManager() {
      if (this.currentRequest) {
        this.currentRequest.cancel()
        this.currentRequest = null
      }
      
      this.clearCache()
      this.requestQueue = []
    }
  },

  watch: {
    dataSource: {
      handler() {
        this.resetError()
        this.loadData()
      },
      immediate: false
    },

    currentPage() {
      if (this.serverSidePagination) {
        this.loadData()
      }
    },

    pageSize() {
      if (this.serverSidePagination) {
        this.loadData()
      }
    },

    searchTerm() {
      if (this.serverSidePagination) {
        this.loadData()
      }
    },

    sortColumn() {
      if (this.serverSidePagination) {
        this.loadData()
      }
    },

    sortDirection() {
      if (this.serverSidePagination) {
        this.loadData()
      }
    }
  }
}
